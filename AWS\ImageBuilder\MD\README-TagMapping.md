# EC2 Tag to SLM_Config.json Mapping

This document explains how EC2 instance tags map to the SLM_Config.json configuration structure.

## Tag Mapping Overview

The EC2 deployment user-data script reads EC2 instance tags and maps them to the SLM_Config.json structure:

| EC2 Tag | Config Path | Example Values | Description |
|---------|-------------|----------------|-------------|
| `Business-Unit` | `business_units.{BU}` | `SPF`, `SC` | Business unit identifier |
| `Environment` | `environments.{ENV}` | `PRD`, `PPE`, `DEV` | Environment identifier |
| `Server-Role` | `APP_TYPE.{TYPE}` | `Shared`, `MSSQL` | Application/server type |

## Configuration Structure

### SLM_Config.json Structure
```json
{
  "CLIENT": "Sanlam",
  "business_units": {
    "{BU}": {                    // ← Business-Unit tag
      "environments": {
        "{ENV}": {               // ← Environment tag
          "ENV": "PRD",
          "domain": "sanlam.co.za",
          "basePath": "OU=Servers,OU=SanlamLife...",
          "DEFAULT_ADM": [
            "MUD\\DL-Sanlamlife-TSMSLocalAdmin",
            "MUD\\DL-Sanlamlife-ADDMDisLocalAdmin"
          ],
          "APP_TYPE": {
            "{TYPE}": "OU=..."   // ← Server-Role tag
          }
        }
      }
    }
  }
}
```

## Example Mappings

### Example 1: Production SQL Server
**EC2 Tags:**
```
Business-Unit: SPF
Environment: PRD
Server-Role: MSSQL
```

**Maps to Config Path:**
```
business_units.SPF.environments.PRD.APP_TYPE.MSSQL
```

**Resolves to:**
- **Admin Groups**: `["MUD\\DL-Sanlamlife-TSMSLocalAdmin", "MUD\\DL-Sanlamlife-ADDMDisLocalAdmin", "MUD\\svcbmcadispaccount"]`
- **Domain**: `sanlam.co.za`
- **OU Path**: `OU=SQL Server,OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=sanlam,DC=co,DC=za`

### Example 2: Development Shared Server
**EC2 Tags:**
```
Business-Unit: SPF
Environment: DEV
Server-Role: Shared
```

**Maps to Config Path:**
```
business_units.SPF.environments.DEV.APP_TYPE.Shared
```

**Resolves to:**
- **Admin Groups**: `["DG-SanlamLife-DEVLocalAdmin"]`
- **Domain**: `mud.internal.co.za`
- **OU Path**: `OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za`

### Example 3: Pre-Production Shared Server
**EC2 Tags:**
```
Business-Unit: SC
Environment: PPE
Server-Role: Shared
```

**Maps to Config Path:**
```
business_units.SC.environments.PPE.APP_TYPE.Shared
```

**Resolves to:**
- **Admin Groups**: `["DG-SanlamLife-PPELocalAdmin"]`
- **Domain**: `ppe.internal.co.za`
- **OU Path**: `OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=ppe,DC=internal,DC=co,DC=za`

## Script Behavior

### 1. Tag Retrieval
```powershell
# Get instance tags from metadata service
$token = Invoke-RestMethod -Uri "http://***************/latest/api/token" -Method PUT
$tagsResponse = Invoke-RestMethod -Uri "http://***************/latest/meta-data/tags/instance"

# Parse tags
switch ($tag) {
    "Business-Unit" { $businessUnit = $tagValue }  # Maps to BU
    "Environment" { $environment = $tagValue }     # Maps to ENV  
    "Server-Role" { $serverRole = $tagValue }      # Maps to APP_TYPE
}
```

### 2. Configuration Navigation
```powershell
# Download SLM_Config.json from S3
aws s3 cp "s3://$s3Bucket/configs/SLM_Config.json" $slmConfigPath

# Navigate configuration structure
$slmConfig = Get-Content $slmConfigPath -Raw | ConvertFrom-Json
$buConfig = $slmConfig.business_units.$businessUnit      # Use BU
$envConfig = $buConfig.environments.$environment         # Use ENV
$appTypeConfig = $envConfig.APP_TYPE.$serverRole        # Use APP_TYPE

# Extract admin groups
$adminGroups = $envConfig.DEFAULT_ADM
```

### 3. Environment Variables Set
The script sets these environment variables for other scripts to use:
```powershell
[Environment]::SetEnvironmentVariable("BUSINESS_UNIT", $businessUnit, "Machine")    # SPF/SC
[Environment]::SetEnvironmentVariable("ENVIRONMENT", $environment, "Machine")       # PRD/PPE/DEV
[Environment]::SetEnvironmentVariable("SERVER_ROLE", $serverRole, "Machine")        # Shared/MSSQL
[Environment]::SetEnvironmentVariable("DOMAIN", $envConfig.domain, "Machine")       # Domain FQDN
[Environment]::SetEnvironmentVariable("BASE_PATH", $envConfig.basePath, "Machine")  # Base OU path
[Environment]::SetEnvironmentVariable("SERVER_OU_PATH", $appTypeConfig, "Machine")  # Specific OU path
```

## Default Values

If EC2 tags are not found, the script uses these defaults:
- **Business-Unit**: `SPF`
- **Environment**: `PRD`
- **Server-Role**: `Shared`

## Validation

The script validates the configuration path exists:
```powershell
# Check business unit exists
if (!$buConfig) {
    throw "Business unit '$businessUnit' not found. Available: $($slmConfig.business_units.PSObject.Properties.Name -join ', ')"
}

# Check environment exists
if (!$envConfig) {
    throw "Environment '$environment' not found. Available: $($buConfig.environments.PSObject.Properties.Name -join ', ')"
}

# Check APP_TYPE exists (warning only)
if (!$appTypeConfig) {
    Write-Log "APP_TYPE '$serverRole' not found. Available: $($envConfig.APP_TYPE.PSObject.Properties.Name -join ', ')" "WARNING"
}
```

## Troubleshooting

### Common Issues

1. **Tag not found**: Check EC2 instance has the required tags set
2. **Config path not found**: Verify SLM_Config.json structure matches expected format
3. **S3 download fails**: Check IAM permissions and S3 bucket access
4. **Admin groups not applied**: Check domain connectivity and group names

### Debug Information

Check these log files:
- `C:\Scripts\ec2-deployment.log` - Main deployment log
- `C:\Scripts\deployment-info.json` - Deployment summary with resolved paths

### Example Deployment Info
```json
{
  "DeploymentDate": "2024-01-15 10:30:00",
  "InstanceId": "i-1234567890abcdef0",
  "BusinessUnit": "SPF",
  "Environment": "PRD", 
  "ServerRole": "MSSQL",
  "Domain": "sanlam.co.za",
  "BasePath": "OU=Servers,OU=SanlamLife,OU=Businesses,DC=sanlam,DC=co,DC=za",
  "ServerOUPath": "OU=SQL Server,OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=sanlam,DC=co,DC=za",
  "AdminGroupsConfigured": [
    "MUD\\DL-Sanlamlife-TSMSLocalAdmin",
    "MUD\\DL-Sanlamlife-ADDMDisLocalAdmin",
    "MUD\\svcbmcadispaccount"
  ],
  "ConfigStructure": {
    "BU": "SPF",
    "ENV": "PRD", 
    "APP_TYPE": "MSSQL"
  },
  "DeploymentStatus": "Success"
}
```

This mapping ensures that EC2 instances are automatically configured with the correct admin groups, domain settings, and OU paths based on their tags and the centralized SLM_Config.json configuration.
