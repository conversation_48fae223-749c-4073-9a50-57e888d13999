<powershell>
# Example EC2 User Data Script - S3 Configuration Approach
# This script demonstrates pulling admin groups from S3-stored SLM_Config.json

# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Create log file
$logFile = "C:\Scripts\user-data.log"
if (!(Test-Path "C:\Scripts")) {
    New-Item -ItemType Directory -Path "C:\Scripts" -Force | Out-Null
}

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path $logFile -Value $logMessage
}

Write-Log "Starting EC2 User Data script execution..."

try {
    # Configuration parameters - these should come from EC2 tags or instance metadata
    $businessUnit = "SPF"  # Get from EC2 tag: Business-Unit
    $environment = "PRD"   # Get from EC2 tag: Environment
    $s3Bucket = "your-deployment-bucket"  # Your S3 bucket name
    $region = "us-east-1"  # Your AWS region
    
    Write-Log "Configuration: Business Unit = $businessUnit, Environment = $environment"
    
    # Download SLM configuration from S3
    $slmConfigS3Key = "configs/SLM_Config.json"
    $slmConfigPath = "C:\Scripts\SLM_Config.json"
    
    Write-Log "Downloading SLM configuration from S3: s3://$s3Bucket/$slmConfigS3Key"
    
    # Download the configuration file
    aws s3 cp "s3://$s3Bucket/$slmConfigS3Key" $slmConfigPath --region $region 2>&1 | Out-Null
    
    if ($LASTEXITCODE -ne 0 -or !(Test-Path $slmConfigPath)) {
        throw "Failed to download SLM_Config.json from S3"
    }
    
    Write-Log "Successfully downloaded SLM configuration from S3"
    
    # Load SLM configuration and extract admin groups
    $slmConfig = Get-Content $slmConfigPath -Raw | ConvertFrom-Json
    $adminGroups = $null
    
    # Navigate to the correct business unit and environment
    $buConfig = $slmConfig.business_units.$businessUnit
    if (!$buConfig) {
        throw "Business unit '$businessUnit' not found in SLM configuration"
    }
    
    $envConfig = $buConfig.environments.$environment
    if (!$envConfig) {
        throw "Environment '$environment' not found for business unit '$businessUnit'"
    }
    
    if (!$envConfig.DEFAULT_ADM -or $envConfig.DEFAULT_ADM.Count -eq 0) {
        throw "No DEFAULT_ADM groups defined for $businessUnit/$environment"
    }
    
    $adminGroups = $envConfig.DEFAULT_ADM
    Write-Log "Found admin groups for $businessUnit/$environment: $($adminGroups -join ', ')"
    
    # Add admin groups to local administrators
    Write-Log "Configuring admin groups..."
    
    $currentAdmins = @()
    try {
        $currentAdmins = Get-LocalGroupMember -Group "Administrators" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Name
    } catch {
        Write-Log "Warning: Could not retrieve current administrators: $($_.Exception.Message)" "WARNING"
    }
    
    $addedGroups = @()
    $skippedGroups = @()
    $failedGroups = @()
    
    foreach ($group in $adminGroups) {
        try {
            if ($currentAdmins -contains $group) {
                Write-Log "Group '$group' is already in Administrators group"
                $skippedGroups += $group
                continue
            }
            
            Add-LocalGroupMember -Group "Administrators" -Member $group -ErrorAction Stop
            Write-Log "Successfully added '$group' to Administrators group"
            $addedGroups += $group
            
        } catch {
            Write-Log "Failed to add '$group' to Administrators group: $($_.Exception.Message)" "WARNING"
            $failedGroups += $group
        }
    }
    
    Write-Log "Admin groups configuration completed - Added: $($addedGroups.Count), Skipped: $($skippedGroups.Count), Failed: $($failedGroups.Count)"
    
    # Save admin groups info for auditing
    $adminGroupsInfo = @{
        BusinessUnit = $businessUnit
        Environment = $environment
        ConfiguredGroups = $adminGroups
        AddedGroups = $addedGroups
        SkippedGroups = $skippedGroups
        FailedGroups = $failedGroups
        ConfigurationDate = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        S3Source = "s3://$s3Bucket/$slmConfigS3Key"
    }
    
    $adminGroupsInfoPath = "C:\Scripts\admin-groups-info.json"
    $adminGroupsInfo | ConvertTo-Json -Depth 10 | Out-File -FilePath $adminGroupsInfoPath -Encoding UTF8
    Write-Log "Admin groups information saved to: $adminGroupsInfoPath"
    
    # Continue with other user-data tasks...
    Write-Log "Continuing with other configuration tasks..."
    
    # Example: Domain join (if needed)
    # $domainUser = "domain\serviceaccount"
    # $domainPassword = ConvertTo-SecureString "password" -AsPlainText -Force
    # $domainCreds = New-Object System.Management.Automation.PSCredential($domainUser, $domainPassword)
    # Add-Computer -DomainName "your.domain.com" -Credential $domainCreds -Restart
    
    # Example: Install software based on server role
    # $serverRole = "WebServer"  # Get from EC2 tag: Server-Role
    # switch ($serverRole) {
    #     "WebServer" { 
    #         Write-Log "Installing IIS for Web Server role"
    #         Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole -All -NoRestart
    #     }
    #     "SQLServer" { 
    #         Write-Log "Preparing system for SQL Server"
    #         Enable-WindowsOptionalFeature -Online -FeatureName NetFx3 -All -NoRestart
    #     }
    # }
    
    Write-Log "User data script completed successfully"
    
} catch {
    Write-Log "Error in user data script: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    
    # Create error flag file for troubleshooting
    $errorInfo = @{
        Error = $_.Exception.Message
        StackTrace = $_.ScriptStackTrace
        Timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    }
    $errorInfo | ConvertTo-Json | Out-File -FilePath "C:\Scripts\user-data-error.json" -Encoding UTF8
    
    # Exit with error code
    exit 1
}
</powershell>
