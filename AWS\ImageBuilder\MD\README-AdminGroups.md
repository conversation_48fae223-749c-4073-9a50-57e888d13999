# Administrator Groups Management for EC2 Deployment

This document outlines the approaches for managing AD groups that need to be added to the local administrators group during EC2 deployment.

## Overview

During EC2 deployment, specific Active Directory groups need to be added to the local administrators group based on the environment (PRD, DEV, PPE). This document describes the recommended approaches and implementation.

## Admin Groups by Environment

### Production (PRD)
- `MUD\DL-Sanlamlife-TSMSLocalAdmin`
- `MUD\DL-Sanlamlife-ADDMDisLocalAdmin`
- `MUD\svcbmcadispaccount`

### Development (DEV)
- `DG-SanlamLife-DEVLocalAdmin`

### Pre-Production (PPE)
- `DG-SanlamLife-PPELocalAdmin`

## Recommended Approach: JSON Configuration

### Option 1: Enhanced SLM_Config.json (Recommended)

The `SLM_Config.json` file has been enhanced to include `DEFAULT_ADM` arrays for each environment:

```json
{
  "CLIENT": "Sanlam",
  "business_units": {
    "SPF": {
      "environments": {
        "PRD": {
          "ENV": "PRD",
          "domain": "sanlam.co.za",
          "DEFAULT_ADM": [
            "MUD\\DL-Sanlamlife-TSMSLocalAdmin",
            "MUD\\DL-Sanlamlife-ADDMDisLocalAdmin", 
            "MUD\\svcbmcadispaccount"
          ],
          "APP_TYPE": { ... }
        },
        "DEV": {
          "ENV": "DEV",
          "domain": "mud.internal.co.za",
          "DEFAULT_ADM": ["DG-SanlamLife-DEVLocalAdmin"],
          "APP_TYPE": { ... }
        }
      }
    }
  }
}
```

**Benefits:**
- ✅ Centralized configuration management
- ✅ Environment-specific settings
- ✅ Easy to maintain and update
- ✅ Consistent with existing infrastructure patterns
- ✅ Version controlled
- ✅ Supports multiple business units

## Implementation Methods

### Method 1: User-Data Script Integration

The user-data scripts have been enhanced to automatically:

1. Download the SLM_Config.json from S3
2. Extract the appropriate admin groups based on business unit and environment
3. Add groups to local administrators
4. Log all actions for auditing
5. Provide fallback groups if configuration is unavailable

**Files Updated:**
- `AWS\ImageBuilder\templates\user-data-template.ps1` - Enhanced with admin groups functionality
- `AWS\EC2-Deployment\generate-user-data.ps1` - Updated to support admin groups

### Method 2: Helper Functions

Created dedicated PowerShell functions for admin group management:

**File:** `AWS\EC2-Deployment\Get-AdminGroups.ps1`

**Functions:**
- `Get-AdminGroups` - Retrieves admin groups from configuration
- `Add-AdminGroupsToLocal` - Adds groups to local administrators
- `Set-DefaultAdminGroups` - Complete workflow function

**Usage Example:**
```powershell
# Import the helper functions
. "C:\Scripts\Get-AdminGroups.ps1"

# Set admin groups for SPF/PRD environment
$success = Set-DefaultAdminGroups -ConfigPath "C:\Scripts\SLM_Config.json" -BusinessUnit "SPF" -Environment "PRD"
```

### Method 3: Systems Manager Parameter Store (Alternative)

For additional security, admin groups can be stored in AWS Systems Manager Parameter Store:

```powershell
# Store admin groups in Parameter Store
aws ssm put-parameter --name "/ec2/admin-groups/prd" --value "MUD\DL-Sanlamlife-TSMSLocalAdmin,MUD\DL-Sanlamlife-ADDMDisLocalAdmin,MUD\svcbmcadispaccount" --type "StringList"

# Retrieve in user-data script
$adminGroups = (aws ssm get-parameter --name "/ec2/admin-groups/$environment" --query "Parameter.Value" --output text).Split(',')
```

## Deployment Workflow

### Step 1: Deploy Configuration to S3

First, deploy your SLM_Config.json to S3:

```powershell
# Deploy configuration to S3
.\Deploy-ConfigToS3.ps1 -S3Bucket "your-deployment-bucket" -Region "us-east-1"

# Include helper scripts (optional)
.\Deploy-ConfigToS3.ps1 -S3Bucket "your-deployment-bucket" -IncludeHelperScripts

# Dry run to test
.\Deploy-ConfigToS3.ps1 -S3Bucket "your-deployment-bucket" -DryRun
```

### Step 2: Automatic Configuration (Recommended)

1. **EC2 Launch**: Instance starts with user-data script
2. **Config Download**: Script downloads SLM_Config.json from S3
3. **Group Extraction**: Script identifies admin groups for the environment
4. **Group Addition**: Script adds groups to local administrators
5. **Logging**: All actions logged to `C:\Scripts\user-data.log`
6. **Audit Trail**: Admin groups info saved to `C:\Scripts\admin-groups-info.json`

### Step 3: Manual Configuration (If Needed)

If automatic configuration fails, use the helper functions:

```powershell
# Download configuration
aws s3 cp s3://your-bucket/configs/SLM_Config.json C:\Scripts\

# Run admin groups configuration
. "C:\Scripts\Get-AdminGroups.ps1"
Set-DefaultAdminGroups -ConfigPath "C:\Scripts\SLM_Config.json" -BusinessUnit "SPF" -Environment "PRD"
```

## Error Handling

The implementation requires the S3 configuration to be available:

1. **Primary**: Read from SLM_Config.json downloaded from S3
2. **Failure Mode**: If S3 download fails, deployment fails with clear error message
3. **Manual Recovery**: Download configuration manually and run helper functions
4. **Logging**: All failures logged with detailed error messages

**Note**: Hardcoded fallback groups have been removed to ensure consistency with the centralized configuration.

## Monitoring and Auditing

### Log Files
- `C:\Scripts\user-data.log` - Complete user-data execution log
- `C:\Scripts\admin-groups.log` - Dedicated admin groups log

### Audit Information
- `C:\Scripts\admin-groups-info.json` - Detailed admin groups configuration info
- `C:\Scripts\business-config.json` - Business configuration used

### Verification
Use the quality check scripts to verify admin groups are correctly configured:
- `Powershell\Auto Services\ServerQualityChecks-latest.ps1`

## Best Practices

1. **Use JSON Configuration**: Store admin groups in SLM_Config.json for centralized management
2. **Version Control**: Keep configuration files in version control
3. **S3 Storage**: Store configurations in S3 for reliable access during deployment
4. **Logging**: Always log admin group changes for security auditing
5. **Testing**: Test admin group configuration in DEV/PPE before PRD deployment
6. **Fallbacks**: Implement fallback mechanisms for resilient deployment
7. **Validation**: Verify admin groups are correctly added after deployment

## Security Considerations

- Admin groups are environment-specific to maintain security boundaries
- All admin group changes are logged for audit trails
- Fallback groups are hardcoded to prevent deployment failures
- Configuration files should be stored securely in S3 with appropriate IAM permissions

## Troubleshooting

### Common Issues

1. **Groups not added**: Check domain connectivity and group names
2. **S3 download fails**: Verify IAM permissions and S3 bucket access
3. **Configuration not found**: Ensure SLM_Config.json is uploaded to S3
4. **Group already exists**: Normal behavior, groups are skipped if already present

### Debug Steps

1. Check user-data logs: `C:\Scripts\user-data.log`
2. Verify configuration: `C:\Scripts\business-config.json`
3. Check admin groups info: `C:\Scripts\admin-groups-info.json`
4. Manually verify local administrators: `Get-LocalGroupMember -Group "Administrators"`
